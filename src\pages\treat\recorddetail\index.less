@import "../../../resources/style/mixins";
@import "../../../resources/style/common";


page {
  /* 页面样式 */
}

.p-page {
  padding-bottom: 30rpx;
}

.page-main {
  padding: 32rpx 0;
  margin: 0 24rpx;
  border-radius: 8rpx;
  background-color: #fff;
}

.m-code {
  display: none;
  margin: 0 32rpx 32rpx;
  padding: 0 0 32rpx 0;
  border-bottom: 2rpx solid rgba(0, 0, 0, 0.08);

  &.active {
    display: block;
  }

  .code-tit {
    font-size: 34rpx;
    color: @hc-color-title;
    font-weight: 600;
    margin-bottom: 15rpx;
  }

  .code-text {
    font-size: 25rpx;
    color: @hc-color-text;

    p {
      margin: 20rpx 0;
    }
  }

  .code-img {
    margin-top: 20rpx;

    image {
      vertical-align: top;
      width: 100%;
    }
  }
}

.m-retry {
  background-color: #fff;
  padding-top: 60rpx;
  padding-bottom: 60rpx;
  text-align: center;

  .retry-btn {
    display: inline-block;
    vertical-align: top;
    font-size: 34rpx;
    color: @hc-color-primary;
    padding: 0 60rpx;
    height: 70rpx;
    line-height: 70rpx;
    border-radius: 4rpx;
    border: 2rpx solid @hc-color-primary;
  }
}


.success-notice {
  display: flex;
  align-items: center;
  padding: 32rpx;
  background: #F8FFF9;
  border-radius: 8rpx;
  margin: 24rpx;

  .notice-icon {
    width: 48rpx;
    height: 48rpx;
    margin-right: 24rpx;
  }

  .notice-text {
    color: #09BB07;
    font-size: 28rpx;
  }
}

.list-item {
  &[data-status="success"] {
    .item-label {
      color: #09BB07;
    }
  }

  &[data-status="abnormal"] {
    .item-label {
      color: #FF5500;
    }
  }
}


[data-from="success"] {
  .list-tit {
    color: @success-color;
  }
}


[data-from="normal"] {
  .list-tit {
    color: @hc-color-title;
  }
}

.m-list {
  padding-bottom: 32rpx;
  margin: 0 32rpx 32rpx;
  border-bottom: 2rpx solid rgba(0, 0, 0, 0.08);

  &:last-child {
    border: none;
    padding-bottom: 0;
    margin-bottom: 0;
  }

  .list-tit {
    font-weight: 600;
    font-size: 34rpx;
    color: @hc-color-title;
  }

  .list {
    padding-top: 32rpx;
  }

  .list-item {
    padding-bottom: 16rpx;
    font-size: 30rpx;
    display: flex;
    align-items: center;

    &:last-child {
      padding: 0;
    }

    &[data-amount] {
      align-items: flex-start;

      .item-label {
        flex: none;
      }

      .item-value {
        flex: 1;
        display: flex;
        justify-content: flex-end;
      }
    }
  }

  .item-label {
    color: rgba(0, 0, 0, .4);
    font-size: 32rpx;
    min-width: 5em;
  }

  .item-value {
    color: @hc-color-title;
    flex: 1;
    word-wrap: break-word;
    white-space: pre-wrap;
    overflow: hidden;
    margin-left: 70rpx;

    &.item-value-right {
      text-align: right;
    }
  }

  .item-value2 {
    color: @hc-color-title;
    flex: 1;
    word-wrap: break-word;
    white-space: pre-wrap;
    overflow: hidden;
    margin-left: 70rpx;
  }

  .unit-price {
    font-size: 32rpx;

  }

  .unit-price3,
  .unit-price2 {
    font-size: 32rpx;
    text-align: right;
    flex: 1;
    padding-left: 0;
    min-width: 40%;
  }
}

.m-table {
  background-color: @hc-color-bg;

  // .table {
  //   background-color: #fff;
  //   margin-bottom: 20rpx;
  //   border-radius: 4rpx;
  //   border: 2rpx solid rgba(0, 0, 0, 0.08);

  //   &:last-child {
  //     margin-bottom: 0;
  //   }
  // }


  .tb-head {
    .head-tr {
      display: flex;
      align-items: stretch;
      padding: 0 16rpx;
      background-color: #f5f5f5;
    }

    .head-td {
      padding: 16rpx 0;
      color: @hc-color-title;
      font-size: 20rpx;
      font-weight: 700 !important; //  确保穿透
      text-align: center;
      white-space: nowrap;
      border-right: 2rpx solid @hc-color-border;

      &:last-child {
        border: none;
        text-align: right;
      }

      &.td-1 {
        flex-basis: 15%;
      }

      &.td-2 {
        flex-basis: 40%;
      }

      &.td-center {
        text-align: center;
      }

      &.td-right {
        text-align: right;
      }
    }
  }

  .tb-extra {
    display: flex;
    padding: 20rpx 30rpx;
    align-items: center;
    border-bottom: 2rpx solid #E5E5E5;
  }

  .extra-tit {
    flex: 1;
    font-size: 34rpx;
    color: @hc-color-title;
  }

  .extra-txt {
    font-size: 28rpx;
    color: @hc-color-text;
  }

  .extra-num {
    font-size: 36rpx;
    color: @hc-color-title;
    margin-left: 20rpx;
  }

  .body-tr {
    display: flex;
    align-items: stretch;
    border-bottom: 2rpx solid @hc-color-border;
    padding: 0 16rpx;
  }

  .body-tr {
    &:last-child {
      border: none;
    }
  }

  .empty-tr {
    display: block;
    text-align: center;
    padding: 20rpx 0
  }

  .body-td {
    border-right: 2rpx solid @hc-color-border;
    .textBreak();

    &:last-child {
      border: none;
      text-align: right;
    }

    &.td-1 {
      flex-basis: 15%;
    }

    &.td-2 {
      flex-basis: 40%;
      color: black;
      line-height: 1.4;
      font-size: 22rpx !important;
    }

    &.td-center {

      text-align: center;
      color: black;
    }

    &.td-right {
      text-align: right;
    }
  }

  // .head-td {
  //   padding: 8rpx 0;
  //   color: @hc-color-title;
  //   font-size: 20rpx;
  //   font-weight: 700; 
  //   text-align: center; 
  //   white-space: nowrap; // 防止换行
  // }

  .body-td {
    padding: 16rpx 0;
    color: @hc-color-text;
    font-size: 24rpx;
  }
}

.ad-treat {
  padding: 20rpx 30rpx;
  margin-top: 20rpx;
  background-color: #fff;
  overflow: hidden;

  .ad-content {
    float: left;
    margin-top: 5rpx;
    color: @hc-color-warn;
  }

  .main-btn {
    padding: 0 25rpx;
    font-size: 24rpx;
    height: 52rpx;
    line-height: 52rpx;
    color: @hc-color-primary;
    border: 2rpx solid @hc-color-primary;
    border-radius: 999rpx;
    float: right;
  }
}